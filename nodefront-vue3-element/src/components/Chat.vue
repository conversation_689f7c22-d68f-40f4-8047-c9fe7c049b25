<template>
  <div class="chat-container">
    <div class="chat-messages">
      <div v-for="(msg, idx) in messages" :key="idx" class="chat-message">
        <span class="chat-user">{{ msg.user }}:</span>
        <span class="chat-text">{{ msg.text }}</span>
      </div>
    </div>
    <el-input
      v-model="input"
      placeholder="输入消息，回车发送"
      @keyup.enter="sendMessage"
      class="chat-input"
      clearable
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'

const wsUrl = 'ws://localhost:3000'
const ws = ref(null)
const input = ref('')
const messages = ref([])

function sendMessage() {
  if (input.value.trim() && ws.value && ws.value.readyState === 1) {
    ws.value.send(JSON.stringify({ user: '我', message: input.value }))
    messages.value.push({ user: '我', text: input.value })
    input.value = ''
  }
}

onMounted(() => {
  ws.value = new WebSocket(wsUrl)
  ws.value.onopen = () => {
    ElMessage.success('已连接到聊天服务器')
  }
  ws.value.onmessage = (event) => {
    messages.value.push({ user: '对方', text: event.data })
  }
  ws.value.onerror = () => {
    ElMessage.error('WebSocket 连接出错')
  }
  ws.value.onclose = () => {
    ElMessage.info('WebSocket 连接已关闭')
  }
})

onBeforeUnmount(() => {
  if (ws.value) ws.value.close()
})
</script>

<style scoped>
.chat-container {
  max-width: 500px;
  margin: 40px auto;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  background: #fff;
  display: flex;
  flex-direction: column;
  height: 400px;
}
.chat-messages {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 12px;
}
.chat-message {
  margin-bottom: 8px;
  word-break: break-all;
}
.chat-user {
  font-weight: bold;
  margin-right: 4px;
}
.chat-input {
  width: 100%;
}
</style> 