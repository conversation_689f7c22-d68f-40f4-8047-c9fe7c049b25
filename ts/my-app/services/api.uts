/**
 * API服务类
 * 定义和管理具体的API接口
 */

import request from '../utils/request.uts'
import { getApiConfig } from '../config/api.uts'

// 用户信息接口
interface UserInfo {
	id : number
	username : string
	email : string
	avatar ?: string
	createdAt : string
}

// 登录请求参数
interface LoginParams {
	username : string
	password : string
}

// 登录响应数据
interface LoginResponse {
	token : string
	userInfo : UserInfo
}

// 分页参数
interface PageParams {
	page : number
	pageSize : number
}

// 分页响应数据
interface PageResponse<T> {
	list : T[]
	total : number
	page : number
	pageSize : number
}

// 通用响应数据
interface ApiResponse<T = any> {
	code : number
	message : string
	data : T
	success : boolean
}

class ApiService {
	private request : typeof request

	constructor() {
		this.request = request
		this.initConfig()
		this.setupInterceptors()
	}

	/**
	 * 初始化配置
	 */
	private initConfig() : void {
		const config = getApiConfig()
		this.request.setBaseURL(config.baseURL)
		this.request.setDefaultHeaders(config.headers)
	}

	/**
	 * 设置拦截器
	 */
	private setupInterceptors() : void {
		// 请求拦截器 - 添加token
		this.request.addRequestInterceptor((config) => {
			const token = uni.getStorageSync('token')
			if (token) {
				config.header = {
					...config.header,
					'Authorization': `Bearer ${token}`
				}
			}
			return config
		})

		// 响应拦截器 - 处理token过期
		this.request.addResponseInterceptor((response) => {
			if (response.code === 401) {
				// token过期，清除本地存储并跳转到登录页
				uni.removeStorageSync('token')
				uni.removeStorageSync('userInfo')
				uni.showToast({
					title: '登录已过期，请重新登录',
					icon: 'none'
				})
				// 跳转到登录页
				uni.reLaunch({
					url: '/pages/login/login'
				})
			}
			return response
		})

		// 错误拦截器 - 统一错误处理
		this.request.addErrorInterceptor((error) => {
			console.error('API请求错误:', error)
			return error
		})
	}

	/**
	 * 用户相关API
	 */
	user = {
		/**
		 * 用户登录
		 */
		login: (params : LoginParams) : Promise<ApiResponse<LoginResponse>> => {
			return this.request.post('/api/auth/login', params)
		},

		/**
		 * 用户注册
		 */
		register: (params : {
			username : string
			email : string
			password : string
		}) : Promise<ApiResponse<UserInfo>> => {
			return this.request.post('/api/auth/register', params)
		},

		/**
		 * 获取用户信息
		 */
		getInfo: () : Promise<ApiResponse<UserInfo>> => {
			return this.request.get('/api/auth/me')
		},

		/**
		 * 更新用户信息
		 */
		updateInfo: (params : Partial<UserInfo>) : Promise<ApiResponse<UserInfo>> => {
			return this.request.put('/api/user/info', params)
		},

		/**
		 * 用户登出
		 */
		logout: () : Promise<ApiResponse<null>> => {
			return this.request.post('/api/user/logout')
		}
	}

	/**
	 * 文件上传相关API
	 */
	upload = {
		/**
		 * 上传文件
		 */
		file: (filePath : string, onProgress ?: (progress : number) => void) : Promise<ApiResponse<{
			url : string
			filename : string
		}>> => {
			return new Promise((resolve, reject) => {
				uni.uploadFile({
					url: getApiConfig().baseURL + '/api/upload/file',
					filePath: filePath,
					name: 'file',
					header: {
						'Authorization': `Bearer ${uni.getStorageSync('token')}`
					},
					success: (res) => {
						try {
							const data = JSON.parse(res.data)
							resolve(data)
						} catch (error) {
							reject(error)
						}
					},
					fail: reject
				})
			})
		},

		/**
		 * 上传图片
		 */
		image: (filePath : string) : Promise<ApiResponse<{
			url : string
			filename : string
		}>> => {
			return this.upload.file(filePath)
		}
	}

	/**
	 * 通用CRUD API
	 */
	crud = {
		/**
		 * 获取列表
		 */
		getList: <T>(url : string, params ?: any) : Promise<ApiResponse<PageResponse<T>>> => {
			return this.request.get(url, params)
		},

		/**
		 * 获取详情
		 */
		getDetail: <T>(url : string, id : number | string) : Promise<ApiResponse<T>> => {
			return this.request.get(`${url}/${id}`)
		},

		/**
		 * 创建
		 */
		create: <T>(url : string, data : any) : Promise<ApiResponse<T>> => {
			return this.request.post(url, data)
		},

		/**
		 * 更新
		 */
		update: <T>(url : string, id : number | string, data : any) : Promise<ApiResponse<T>> => {
			return this.request.put(`${url}/${id}`, data)
		},

		/**
		 * 删除
		 */
		delete: (url : string, id : number | string) : Promise<ApiResponse<null>> => {
			return this.request.delete(`${url}/${id}`)
		}
	}
}

// 创建API服务实例
const apiService = new ApiService()

// 导出API服务实例和类型
export default apiService
export {
	type UserInfo,
	type LoginParams,
	type LoginResponse,
	type PageParams,
	type PageResponse,
	type ApiResponse
}