/**
 * API配置文件
 * 管理不同环境的API配置
 */

// 环境类型
type Environment = 'development' | 'production' | 'test'

// API配置接口
interface ApiConfig {
	baseURL : string
	timeout : number
	headers : Record<string, string>
}

// 环境配置
const ENV_CONFIG : Record<Environment, ApiConfig> = {
	development: {
		baseURL: 'http://localhost:3000',
		timeout: 10000,
		headers: {
			'Content-Type': 'application/json',
			'X-Environment': 'development'
		}
	},
	test: {
		baseURL: 'https://api-test.example.com',
		timeout: 10000,
		headers: {
			'Content-Type': 'application/json',
			'X-Environment': 'test'
		}
	},
	production: {
		baseURL: 'https://api.example.com',
		timeout: 15000,
		headers: {
			'Content-Type': 'application/json',
			'X-Environment': 'production'
		}
	}
}

// 获取当前环境
function getCurrentEnvironment() : Environment {
	// #ifdef APP-PLUS
	// 在APP环境下，可以通过编译条件判断
	return 'development'
	// #endif

	// #ifdef H5
	// 在H5环境下，可以通过域名判断
	const hostname = window.location.hostname
	if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
		return 'development'
	} else if (hostname.includes('test')) {
		return 'test'
	} else {
		return 'production'
	}
	// #endif

	// 默认返回开发环境
	return 'development'
}

// 获取当前环境的API配置
function getApiConfig() : ApiConfig {
	const env = getCurrentEnvironment()
	return ENV_CONFIG[env]
}

// 导出配置
export {
	getCurrentEnvironment,
	getApiConfig,
	type Environment,
	type ApiConfig
}