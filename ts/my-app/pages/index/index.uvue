<template>
	<view class="content">
		<!-- 用户信息展示 -->
		<view class="user-info" v-if="userInfo">
			<text class="user-title">用户信息</text>
			<text class="user-item">用户名: {{ userInfo.username }}</text>
			<text class="user-item">邮箱: {{ userInfo.email }}</text>
		</view>

		<!-- 操作按钮 -->
		<view class="button-group">
			<button class="btn" @click="handleLogin1">模拟登录1</button>
			<button class="btn" @click="handleLogin">模拟登录</button>
			<button class="btn" @click="handleGetUserInfo">获取用户信息</button>
			<button class="btn" @click="handleUploadImage">上传图片</button>
			<button class="btn" @click="handleGetList">获取列表</button>
		</view>

		<!-- 列表数据展示 -->
		<view class="list-container" v-if="listData.length > 0">
			<text class="list-title">列表数据</text>
			<view class="list-item" v-for="(item, index) in listData" :key="index">
				<text class="item-text">{{ item.name || item.title || `项目 ${index + 1}` }}</text>
			</view>
		</view>
	</view>
</template>

<script lang="uts">
	import apiService from '../../services/api.uts'
	import type { UserInfo } from '../../services/api.uts'

	export default {
		data() {
			return {
				title: 'Hello',
				userInfo: null as UserInfo | null,
				listData: [] as any[]
			}
		},
		onLoad() {
			console.log('页面加载完成')
		},
		methods: {
			handleLogin1() {
				uni.login({
					"provider": "weixin",
					"onlyAuthorize": true, // 微信登录仅请求授权认证
					success: function (event) {
						const { code } = event
						console.log(code)
						//客户端成功获取授权临时票据（code）,向业务服务器发起登录请求。
						apiService.crud.create("/api/auth/wx-login", { code })
					},
					fail: function (err) {
						// 登录授权失败
						// err.code是错误码
					}
				})
			},
			/**
			 * 模拟登录
			 */
			async handleLogin() {
				try {
					const response = await apiService.user.login({
						username: 'test',
						email: '123456',
						password: "123456"
					})

					if (response.success) {
						// 保存token和用户信息
						uni.setStorageSync('token', response.data.token)
						uni.setStorageSync('userInfo', response.data.userInfo)

						uni.showToast({
							title: '登录成功',
							icon: 'success'
						})

						// 更新页面数据
						this.userInfo = response.data.userInfo
					}
				} catch (error) {
					console.error('登录失败:', error)
					uni.showToast({
						title: '登录失败',
						icon: 'error'
					})
				}
			},

			/**
			 * 获取用户信息
			 */
			async handleGetUserInfo() {
				try {
					const response = await apiService.user.getInfo()

					if (response.success) {
						this.userInfo = response.data
						uni.showToast({
							title: '获取用户信息成功',
							icon: 'success'
						})
					}
				} catch (error) {
					console.error('获取用户信息失败:', error)
					uni.showToast({
						title: '获取用户信息失败',
						icon: 'error'
					})
				}
			},

			/**
			 * 上传图片
			 */
			async handleUploadImage() {
				try {
					// 选择图片
					const chooseResult : any = await uni.chooseImage({
						count: 1,
						sizeType: ['compressed'],
						sourceType: ['album', 'camera']
					})

					if (chooseResult?.tempFilePaths && chooseResult.tempFilePaths.length > 0) {
						const filePath = chooseResult.tempFilePaths[0]

						// 上传图片
						const response = await apiService.upload.image(filePath)

						if (response.success) {
							uni.showToast({
								title: '上传成功',
								icon: 'success'
							})
							console.log('上传结果:', response.data)
						}
					}
				} catch (error) {
					console.error('上传失败:', error)
					uni.showToast({
						title: '上传失败',
						icon: 'error'
					})
				}
			},

			/**
			 * 获取列表数据
			 */
			async handleGetList() {
				try {
					// 使用通用CRUD API获取列表
					const response = await apiService.crud.getList('/api/auth/users', {
						page: 1,
						pageSize: 10
					})
					console.log(response)
					if (response.success) {
						this.listData = response.data
						uni.showToast({
							title: `获取到 ${response.data.length} 条数据`,
							icon: 'success'
						})
					}
				} catch (error) {
					console.error('获取列表失败:', error)
					uni.showToast({
						title: '获取列表失败',
						icon: 'error'
					})
				}
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 20px;
	}

	.logo {
		height: 200rpx;
		width: 200rpx;
		margin-top: 200rpx;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 50rpx;
	}

	.text-area {
		display: flex;
		justify-content: center;
		margin-bottom: 50rpx;
	}

	.title {
		font-size: 36rpx;
		color: #8f8f94;
	}

	.user-info {
		width: 100%;
		margin-bottom: 30rpx;
		padding: 20rpx;
		background-color: #f8f8f8;
		border-radius: 10rpx;
	}

	.user-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
		display: block;
	}

	.user-item {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 5rpx;
		display: block;
	}

	.button-group {
		width: 100%;
		margin-bottom: 30rpx;
	}

	.btn {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #007aff;
		color: white;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		font-size: 28rpx;
	}

	.list-container {
		width: 100%;
	}

	.list-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
	}

	.list-item {
		padding: 20rpx;
		background-color: #f8f8f8;
		border-radius: 10rpx;
		margin-bottom: 10rpx;
	}

	.item-text {
		font-size: 28rpx;
		color: #333;
	}
</style>