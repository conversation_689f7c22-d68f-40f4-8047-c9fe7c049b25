{"code": "import { __awaiter } from \"tslib\";\nimport { defineComponent } from \"vue\";\nimport apiService from \"../../services/api\";\nimport {} from \"../../services/api\";\nexport default defineComponent({\n    data() {\n        return {\n            title: 'Hello',\n            userInfo: null,\n            listData: []\n        };\n    },\n    onLoad() {\n        uni.__f__('log', 'at pages/index/index.uvue:42', '页面加载完成');\n    },\n    methods: {\n        handleLogin1() {\n            uni.login(new UTSJSONObject({\n                \"provider\": \"weixin\",\n                \"onlyAuthorize\": true,\n                success: function (event) {\n                    const code = event.code;\n                    uni.__f__('log', 'at pages/index/index.uvue:51', code);\n                    //客户端成功获取授权临时票据（code）,向业务服务器发起登录请求。\n                    apiService.crud.create(\"/api/auth/wx-login\", new UTSJSONObject({ code }));\n                },\n                fail: function (err) {\n                    // 登录授权失败\n                    // err.code是错误码\n                }\n            }));\n        },\n        /**\n         * 模拟登录\n         */\n        handleLogin() {\n            return __awaiter(this, void 0, void 0, function* () {\n                try {\n                    const response = yield apiService.user.login({\n                        username: 'test',\n                        email: '123456',\n                        password: \"123456\"\n                    });\n                    if (response.success) {\n                        // 保存token和用户信息\n                        uni.setStorageSync('token', response.data.token);\n                        uni.setStorageSync('userInfo', response.data.userInfo);\n                        uni.showToast({\n                            title: '登录成功',\n                            icon: 'success'\n                        });\n                        // 更新页面数据\n                        this.userInfo = response.data.userInfo;\n                    }\n                }\n                catch (error) {\n                    uni.__f__('error', 'at pages/index/index.uvue:86', '登录失败:', error);\n                    uni.showToast({\n                        title: '登录失败',\n                        icon: 'error'\n                    });\n                }\n            });\n        },\n        /**\n         * 获取用户信息\n         */\n        handleGetUserInfo() {\n            return __awaiter(this, void 0, void 0, function* () {\n                try {\n                    const response = yield apiService.user.getInfo();\n                    if (response.success) {\n                        this.userInfo = response.data;\n                        uni.showToast({\n                            title: '获取用户信息成功',\n                            icon: 'success'\n                        });\n                    }\n                }\n                catch (error) {\n                    uni.__f__('error', 'at pages/index/index.uvue:109', '获取用户信息失败:', error);\n                    uni.showToast({\n                        title: '获取用户信息失败',\n                        icon: 'error'\n                    });\n                }\n            });\n        },\n        /**\n         * 上传图片\n         */\n        handleUploadImage() {\n            return __awaiter(this, void 0, void 0, function* () {\n                try {\n                    // 选择图片\n                    const chooseResult = yield uni.chooseImage(new UTSJSONObject({\n                        count: 1,\n                        sizeType: ['compressed'],\n                        sourceType: ['album', 'camera']\n                    }));\n                    if ((chooseResult === null || chooseResult === void 0 ? null : chooseResult.tempFilePaths) && chooseResult.tempFilePaths.length > 0) {\n                        const filePath = chooseResult.tempFilePaths[0];\n                        // 上传图片\n                        const response = yield apiService.upload.image(filePath);\n                        if (response.success) {\n                            uni.showToast({\n                                title: '上传成功',\n                                icon: 'success'\n                            });\n                            uni.__f__('log', 'at pages/index/index.uvue:140', '上传结果:', response.data);\n                        }\n                    }\n                }\n                catch (error) {\n                    uni.__f__('error', 'at pages/index/index.uvue:144', '上传失败:', error);\n                    uni.showToast({\n                        title: '上传失败',\n                        icon: 'error'\n                    });\n                }\n            });\n        },\n        /**\n         * 获取列表数据\n         */\n        handleGetList() {\n            return __awaiter(this, void 0, void 0, function* () {\n                try {\n                    // 使用通用CRUD API获取列表\n                    const response = yield apiService.crud.getList('/api/auth/users', new UTSJSONObject({\n                        page: 1,\n                        pageSize: 10\n                    }));\n                    uni.__f__('log', 'at pages/index/index.uvue:162', response);\n                    if (response.success) {\n                        this.listData = response.data;\n                        uni.showToast({\n                            title: `获取到 ${response.data.length} 条数据`,\n                            icon: 'success'\n                        });\n                    }\n                }\n                catch (error) {\n                    uni.__f__('error', 'at pages/index/index.uvue:171', '获取列表失败:', error);\n                    uni.showToast({\n                        title: '获取列表失败',\n                        icon: 'error'\n                    });\n                }\n            });\n        }\n    }\n});\n//# sourceMappingURL=/Users/<USER>/nodefront/ts/my-app/pages/index/index.uvue?vue&type=script&lang.uts.js.map", "references": [], "uniExtApis": ["uni.__f__", "uni.login", "uni.setStorageSync", "uni.showToast", "uni.chooseImage"], "map": "{\"version\":3,\"file\":\"index.uvue?vue&type=script&lang.uts.js\",\"sourceRoot\":\"\",\"sources\":[\"index.uvue?vue&type=script&lang.uts\"],\"names\":[],\"mappings\":\";;AACC,OAAO,UAAU,2BAA8B;AAC/C,OAAY,EAAY,2BAA8B;AAEtD,+BAAe;IACd,IAAI;QACH,OAAO;YACN,KAAK,EAAE,OAAO;YACd,QAAQ,EAAE,IAAuB;YACjC,QAAQ,EAAE,EAAW;SACrB,CAAA;IACF,CAAC;IACD,MAAM;QACL,GAAG,CAAC,KAAK,CAAC,KAAK,EAAC,8BAA8B,EAAC,QAAQ,CAAC,CAAA;IACzD,CAAC;IACD,OAAO,EAAE;QACR,YAAY;YACX,GAAG,CAAC,KAAK,mBAAC;gBACT,UAAU,EAAE,QAAQ;gBACpB,eAAe,EAAE,IAAI;gBACrB,OAAO,EAAE,UAAU,KAAK;oBACf,MAAA,IAAI,GAAK,KAAK,KAAV,CAAU;oBACtB,GAAG,CAAC,KAAK,CAAC,KAAK,EAAC,8BAA8B,EAAC,IAAI,CAAC,CAAA;oBACpD,mCAAmC;oBACnC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,oBAAE,EAAE,IAAI,EAAE,EAAC,CAAA;gBACvD,CAAC;gBACD,IAAI,EAAE,UAAU,GAAG;oBAClB,SAAS;oBACT,eAAe;gBAChB,CAAC;aACD,EAAC,CAAA;QACH,CAAC;QACD;;WAEG;QACG,WAAW;;gBAChB,IAAI;oBACH,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;wBAC5C,QAAQ,EAAE,MAAM;wBAChB,KAAK,EAAE,QAAQ;wBACf,QAAQ,EAAE,QAAQ;qBAClB,CAAC,CAAA;oBAEF,IAAI,QAAQ,CAAC,OAAO,EAAE;wBACrB,eAAe;wBACf,GAAG,CAAC,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;wBAChD,GAAG,CAAC,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;wBAEtD,GAAG,CAAC,SAAS,CAAC;4BACb,KAAK,EAAE,MAAM;4BACb,IAAI,EAAE,SAAS;yBACf,CAAC,CAAA;wBAEF,SAAS;wBACT,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAA;qBACtC;iBACD;gBAAC,OAAO,KAAK,EAAE;oBACf,GAAG,CAAC,KAAK,CAAC,OAAO,EAAC,8BAA8B,EAAC,OAAO,EAAE,KAAK,CAAC,CAAA;oBAChE,GAAG,CAAC,SAAS,CAAC;wBACb,KAAK,EAAE,MAAM;wBACb,IAAI,EAAE,OAAO;qBACb,CAAC,CAAA;iBACF;YACF,CAAC;SAAA;QAED;;WAEG;QACG,iBAAiB;;gBACtB,IAAI;oBACH,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;oBAEhD,IAAI,QAAQ,CAAC,OAAO,EAAE;wBACrB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAA;wBAC7B,GAAG,CAAC,SAAS,CAAC;4BACb,KAAK,EAAE,UAAU;4BACjB,IAAI,EAAE,SAAS;yBACf,CAAC,CAAA;qBACF;iBACD;gBAAC,OAAO,KAAK,EAAE;oBACf,GAAG,CAAC,KAAK,CAAC,OAAO,EAAC,+BAA+B,EAAC,WAAW,EAAE,KAAK,CAAC,CAAA;oBACrE,GAAG,CAAC,SAAS,CAAC;wBACb,KAAK,EAAE,UAAU;wBACjB,IAAI,EAAE,OAAO;qBACb,CAAC,CAAA;iBACF;YACF,CAAC;SAAA;QAED;;WAEG;QACG,iBAAiB;;gBACtB,IAAI;oBACH,OAAO;oBACP,MAAM,YAAY,GAAS,MAAM,GAAG,CAAC,WAAW,mBAAC;wBAChD,KAAK,EAAE,CAAC;wBACR,QAAQ,EAAE,CAAC,YAAY,CAAC;wBACxB,UAAU,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;qBAC/B,EAAC,CAAA;oBAEF,IAAI,CAAA,YAAY,aAAZ,YAAY,qBAAZ,YAAY,CAAE,aAAa,KAAI,YAAY,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;wBACzE,MAAM,QAAQ,GAAG,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;wBAE9C,OAAO;wBACP,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;wBAExD,IAAI,QAAQ,CAAC,OAAO,EAAE;4BACrB,GAAG,CAAC,SAAS,CAAC;gCACb,KAAK,EAAE,MAAM;gCACb,IAAI,EAAE,SAAS;6BACf,CAAC,CAAA;4BACF,GAAG,CAAC,KAAK,CAAC,KAAK,EAAC,+BAA+B,EAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAA;yBACvE;qBACD;iBACD;gBAAC,OAAO,KAAK,EAAE;oBACf,GAAG,CAAC,KAAK,CAAC,OAAO,EAAC,+BAA+B,EAAC,OAAO,EAAE,KAAK,CAAC,CAAA;oBACjE,GAAG,CAAC,SAAS,CAAC;wBACb,KAAK,EAAE,MAAM;wBACb,IAAI,EAAE,OAAO;qBACb,CAAC,CAAA;iBACF;YACF,CAAC;SAAA;QAED;;WAEG;QACG,aAAa;;gBAClB,IAAI;oBACH,mBAAmB;oBACnB,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,oBAAE;wBACjE,IAAI,EAAE,CAAC;wBACP,QAAQ,EAAE,EAAE;qBACZ,EAAC,CAAA;oBACF,GAAG,CAAC,KAAK,CAAC,KAAK,EAAC,+BAA+B,EAAC,QAAQ,CAAC,CAAA;oBACzD,IAAI,QAAQ,CAAC,OAAO,EAAE;wBACrB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAA;wBAC7B,GAAG,CAAC,SAAS,CAAC;4BACb,KAAK,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,MAAM;4BACxC,IAAI,EAAE,SAAS;yBACf,CAAC,CAAA;qBACF;iBACD;gBAAC,OAAO,KAAK,EAAE;oBACf,GAAG,CAAC,KAAK,CAAC,OAAO,EAAC,+BAA+B,EAAC,SAAS,EAAE,KAAK,CAAC,CAAA;oBACnE,GAAG,CAAC,SAAS,CAAC;wBACb,KAAK,EAAE,QAAQ;wBACf,IAAI,EAAE,OAAO;qBACb,CAAC,CAAA;iBACF;YACF,CAAC;SAAA;KACD;CACD,EAAA\"}"}