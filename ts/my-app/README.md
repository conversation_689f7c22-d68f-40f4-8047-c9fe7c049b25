# Uni-App X 请求封装

这是一个为 Uni-App X 项目设计的完整请求封装解决方案，提供了强大的网络请求功能，包括拦截器、错误处理、文件上传等特性。

## 功能特性

- ✅ **完整的请求封装** - 支持 GET、POST、PUT、DELETE、PATCH 等请求方法
- ✅ **请求/响应拦截器** - 支持添加多个拦截器，灵活处理请求和响应
- ✅ **错误处理** - 统一的错误处理和用户提示
- ✅ **Token 管理** - 自动添加和刷新 token
- ✅ **文件上传** - 支持图片和文件上传
- ✅ **环境配置** - 支持多环境配置（开发、测试、生产）
- ✅ **类型安全** - 完整的 TypeScript 类型定义
- ✅ **加载提示** - 自动显示和隐藏加载状态
- ✅ **通用 CRUD** - 提供通用的增删改查接口

## 项目结构

```
my-app/
├── utils/
│   └── request.uts          # 核心请求封装类
├── config/
│   └── api.uts             # API配置文件
├── services/
│   └── api.uts             # API服务类
└── pages/
    └── index/
        └── index.uvue      # 使用示例
```

## 快速开始

### 1. 基础使用

```typescript
import request from '@/utils/request.uts'

// GET 请求
const response = await request.get('/api/users', { page: 1 })

// POST 请求
const response = await request.post('/api/users', { name: 'John' })

// PUT 请求
const response = await request.put('/api/users/1', { name: 'Jane' })

// DELETE 请求
const response = await request.delete('/api/users/1')
```

### 2. 使用 API 服务

```typescript
import apiService from '@/services/api.uts'

// 用户登录
const loginResponse = await apiService.user.login({
  username: 'testuser',
  password: '123456'
})

// 获取用户信息
const userInfo = await apiService.user.getInfo()

// 上传图片
const uploadResult = await apiService.upload.image(filePath)

// 获取列表数据
const listData = await apiService.crud.getList('/api/articles', {
  page: 1,
  pageSize: 10
})
```

### 3. 配置环境

在 `config/api.uts` 中配置不同环境的 API 地址：

```typescript
const ENV_CONFIG = {
  development: {
    baseURL: 'https://api-dev.example.com',
    timeout: 10000
  },
  production: {
    baseURL: 'https://api.example.com',
    timeout: 15000
  }
}
```

## 详细使用说明

### 请求配置

```typescript
interface RequestConfig {
  url: string                    // 请求地址
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'  // 请求方法
  data?: any                     // 请求数据
  header?: Record<string, string> // 请求头
  timeout?: number               // 超时时间
  showLoading?: boolean          // 是否显示加载提示
  loadingText?: string           // 加载提示文字
}
```

### 响应数据格式

```typescript
interface ResponseData<T = any> {
  code: number      // 状态码
  message: string   // 消息
  data: T          // 数据
  success: boolean  // 是否成功
}
```

### 拦截器使用

```typescript
// 添加请求拦截器
request.addRequestInterceptor((config) => {
  // 在请求发送前处理
  console.log('发送请求:', config.url)
  return config
})

// 添加响应拦截器
request.addResponseInterceptor((response) => {
  // 在响应返回后处理
  console.log('收到响应:', response)
  return response
})

// 添加错误拦截器
request.addErrorInterceptor((error) => {
  // 处理错误
  console.error('请求错误:', error)
  return error
})
```

### 文件上传

```typescript
// 上传文件
const uploadResult = await apiService.upload.file(filePath, (progress) => {
  console.log('上传进度:', progress)
})

// 上传图片
const imageResult = await apiService.upload.image(filePath)
```

### 通用 CRUD 操作

```typescript
// 获取列表
const list = await apiService.crud.getList('/api/articles', {
  page: 1,
  pageSize: 10
})

// 获取详情
const detail = await apiService.crud.getDetail('/api/articles', 1)

// 创建
const created = await apiService.crud.create('/api/articles', {
  title: '新文章',
  content: '文章内容'
})

// 更新
const updated = await apiService.crud.update('/api/articles', 1, {
  title: '更新的标题'
})

// 删除
await apiService.crud.delete('/api/articles', 1)
```

## 错误处理

请求封装会自动处理以下错误：

1. **网络错误** - 网络连接失败、超时等
2. **HTTP 错误** - 4xx、5xx 状态码
3. **业务错误** - 后端返回的业务错误码
4. **Token 过期** - 自动清除本地存储并跳转登录页

## 自定义配置

### 创建自定义请求实例

```typescript
import { Request } from '@/utils/request.uts'

const customRequest = new Request({
  baseURL: 'https://custom-api.example.com',
  timeout: 20000,
  defaultHeaders: {
    'X-Custom-Header': 'custom-value'
  }
})
```

### 设置默认配置

```typescript
// 设置基础 URL
request.setBaseURL('https://api.example.com')

// 设置默认请求头
request.setDefaultHeaders({
  'Authorization': 'Bearer token',
  'X-Client-Version': '1.0.0'
})
```

## 最佳实践

1. **统一错误处理** - 在拦截器中统一处理错误，避免在每个请求中重复处理
2. **类型安全** - 为 API 响应定义明确的类型接口
3. **环境配置** - 使用环境变量或编译条件来区分不同环境的配置
4. **Token 管理** - 在请求拦截器中自动添加 token，在响应拦截器中处理 token 过期
5. **加载状态** - 合理使用加载提示，提升用户体验

## 注意事项

1. 确保在 `manifest.json` 中配置了网络权限
2. 在真机调试时，确保设备能够访问配置的 API 地址
3. 文件上传功能需要确保服务器支持相应的上传接口
4. Token 过期处理会自动跳转到登录页，请确保登录页路径正确

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础请求方法
- 支持拦截器功能
- 支持文件上传
- 支持多环境配置 