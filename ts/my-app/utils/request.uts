/**
 * 请求封装工具类
 * 支持请求拦截器、响应拦截器、错误处理、超时设置等功能
 */

// 请求配置接口
interface RequestConfig {
	url : string
	method ?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
	data ?: any
	header ?: Record<string, string>
	timeout ?: number
	showLoading ?: boolean
	loadingText ?: string
}

// 响应数据接口
interface ResponseData<T = any> {
	code : number
	message : string
	data : T
	success : boolean
}

// 请求拦截器类型
type RequestInterceptor = (config : RequestConfig) => RequestConfig | Promise<RequestConfig>

// 响应拦截器类型
type ResponseInterceptor<T = any> = (response : ResponseData<T>) => ResponseData<T> | Promise<ResponseData<T>>

// 错误拦截器类型
type ErrorInterceptor = (error : any) => any

class Request {
	private baseURL : string = ''
	private timeout : number = 10000
	private defaultHeaders : Record<string, string> = {
		'Content-Type': 'application/json'
	}

	// 拦截器
	private requestInterceptors : RequestInterceptor[] = []
	private responseInterceptors : ResponseInterceptor[] = []
	private errorInterceptors : ErrorInterceptor[] = []

	// 默认配置
	private defaultConfig : Partial<RequestConfig> = {
		timeout: 10000,
		showLoading: true,
		loadingText: '加载中...'
	}

	constructor(config ?: {
		baseURL ?: string
		timeout ?: number
		defaultHeaders ?: Record<string, string>
	}) {
		if (config?.baseURL) {
			this.baseURL = config.baseURL
		}
		if (config?.timeout) {
			this.timeout = config.timeout
		}
		if (config?.defaultHeaders) {
			this.defaultHeaders = { ...this.defaultHeaders, ...config.defaultHeaders }
		}
	}

	/**
	 * 设置基础URL
	 */
	setBaseURL(url : string) : void {
		this.baseURL = url
	}

	/**
	 * 设置默认请求头
	 */
	setDefaultHeaders(headers : Record<string, string>) : void {
		this.defaultHeaders = { ...this.defaultHeaders, ...headers }
	}

	/**
	 * 添加请求拦截器
	 */
	addRequestInterceptor(interceptor : RequestInterceptor) : void {
		this.requestInterceptors.push(interceptor)
	}

	/**
	 * 添加响应拦截器
	 */
	addResponseInterceptor(interceptor : ResponseInterceptor) : void {
		this.responseInterceptors.push(interceptor)
	}

	/**
	 * 添加错误拦截器
	 */
	addErrorInterceptor(interceptor : ErrorInterceptor) : void {
		this.errorInterceptors.push(interceptor)
	}

	/**
	 * 执行请求拦截器
	 */
	private async executeRequestInterceptors(config : RequestConfig) : Promise<RequestConfig> {
		let result = config
		for (const interceptor of this.requestInterceptors) {
			result = await interceptor(result)
		}
		return result
	}

	/**
	 * 执行响应拦截器
	 */
	private async executeResponseInterceptors<T>(response : ResponseData<T>) : Promise<ResponseData<T>> {
		let result = response
		for (const interceptor of this.responseInterceptors) {
			result = await interceptor(result)
		}
		return result
	}

	/**
	 * 执行错误拦截器
	 */
	private async executeErrorInterceptors(error : any) : Promise<any> {
		let result = error
		for (const interceptor of this.errorInterceptors) {
			result = await interceptor(result)
		}
		return result
	}

	/**
	 * 显示加载提示
	 */
	private showLoading(text : string) : void {
		uni.showLoading({
			title: text,
			mask: true
		})
	}

	/**
	 * 隐藏加载提示
	 */
	private hideLoading() : void {
		uni.hideLoading()
	}

	/**
	 * 显示错误提示
	 */
	private showError(message : string) : void {
		uni.showToast({
			title: message,
			icon: 'error',
			duration: 2000
		})
	}

	/**
	 * 处理网络错误
	 */
	private handleNetworkError(error : any) : void {
		console.error('网络请求错误:', error)

		let errorMessage = '网络请求失败'

		if (error.errMsg) {
			if (error.errMsg.includes('timeout')) {
				errorMessage = '请求超时，请检查网络连接'
			} else if (error.errMsg.includes('fail')) {
				errorMessage = '网络连接失败，请检查网络设置'
			} else {
				errorMessage = error.errMsg
			}
		}

		this.showError(errorMessage)
	}

	/**
	 * 处理业务错误
	 */
	private handleBusinessError(response : ResponseData) : void {
		console.error('业务错误:', response)

		if (response.message) {
			this.showError(response.message)
		} else {
			this.showError('请求失败，请稍后重试')
		}
	}

	/**
	 * 核心请求方法
	 */
	private async request<T = any>(config : RequestConfig) : Promise<ResponseData<T>> {
		// 合并配置
		const finalConfig : RequestConfig = {
			...this.defaultConfig,
			...config,
			header: { ...this.defaultHeaders, ...config.header }
		}

		// 添加基础URL
		if (this.baseURL && !finalConfig.url.startsWith('http')) {
			finalConfig.url = this.baseURL + finalConfig.url
		}

		// 执行请求拦截器
		const interceptedConfig = await this.executeRequestInterceptors(finalConfig)

		// 显示加载提示
		if (interceptedConfig.showLoading) {
			this.showLoading(interceptedConfig.loadingText || '加载中...')
		}

		return new Promise((resolve, reject) => {
			uni.request({
				url: interceptedConfig.url,
				method: interceptedConfig.method || 'GET',
				data: interceptedConfig.data,
				header: interceptedConfig.header,
				timeout: interceptedConfig.timeout || this.timeout,
				success: async (res : any) => {
					// 隐藏加载提示
					if (interceptedConfig.showLoading) {
						this.hideLoading()
					}

					try {
						// 检查HTTP状态码
						if (res.statusCode >= 200 && res.statusCode < 300) {
							// 构造响应数据
							const responseData : ResponseData<T> = {
								code: res.data?.code || res.statusCode,
								message: res.data?.message || '请求成功',
								data: res.data?.data || res.data,
								success: res.data?.success !== false
							}

							// 执行响应拦截器
							const interceptedResponse = await this.executeResponseInterceptors(responseData)

							// 检查业务状态码
							if (interceptedResponse.success && interceptedResponse.code === 200) {
								resolve(interceptedResponse)
							} else {
								this.handleBusinessError(interceptedResponse)
								reject(interceptedResponse)
							}
						} else {
							// HTTP错误
							const error = {
								code: res.statusCode,
								message: `HTTP ${res.statusCode} 错误`,
								data: res.data
							}
							this.handleNetworkError(error)
							reject(error)
						}
					} catch (error) {
						// 执行错误拦截器
						const interceptedError = await this.executeErrorInterceptors(error)
						reject(interceptedError)
					}
				},
				fail: async (error : any) => {
					// 隐藏加载提示
					if (interceptedConfig.showLoading) {
						this.hideLoading()
					}

					// 处理网络错误
					this.handleNetworkError(error)

					// 执行错误拦截器
					const interceptedError = await this.executeErrorInterceptors(error)
					reject(interceptedError)
				}
			})
		})
	}

	/**
	 * GET请求
	 */
	async get<T = any>(url : string, data ?: any, config ?: Partial<RequestConfig>) : Promise<ResponseData<T>> {
		return this.request<T>({
			url,
			method: 'GET',
			data,
			...config
		})
	}

	/**
	 * POST请求
	 */
	async post<T = any>(url : string, data ?: any, config ?: Partial<RequestConfig>) : Promise<ResponseData<T>> {
		return this.request<T>({
			url,
			method: 'POST',
			data,
			...config
		})
	}

	/**
	 * PUT请求
	 */
	async put<T = any>(url : string, data ?: any, config ?: Partial<RequestConfig>) : Promise<ResponseData<T>> {
		return this.request<T>({
			url,
			method: 'PUT',
			data,
			...config
		})
	}

	/**
	 * DELETE请求
	 */
	async delete<T = any>(url : string, data ?: any, config ?: Partial<RequestConfig>) : Promise<ResponseData<T>> {
		return this.request<T>({
			url,
			method: 'DELETE',
			data,
			...config
		})
	}

	/**
	 * PATCH请求
	 */
	async patch<T = any>(url : string, data ?: any, config ?: Partial<RequestConfig>) : Promise<ResponseData<T>> {
		return this.request<T>({
			url,
			method: 'PATCH',
			data,
			...config
		})
	}
}

// 创建默认实例
const request = new Request()

// 导出默认实例和类
export default request
export { Request, type RequestConfig, type ResponseData }